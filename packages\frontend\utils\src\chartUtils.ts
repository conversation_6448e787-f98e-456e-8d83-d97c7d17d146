export interface ChartDataPoint {
  x: Date | number | string;
  y: number;
  [key: string]: any;
}

export interface ChartDataset {
  label: string;
  color: string;
  data: ChartDataPoint[];
  [key: string]: any;
}

export interface ChartTransformOptions {
  maxDataPoints?: number;
  sortByTime?: boolean;
  filterInvalid?: boolean;
  groupBy?: string;
}

/**
 * Predefined color palettes for charts
 */
export const CHART_COLOR_PALETTES = {
  default: [
    '#3B82F6',
    '#EF4444',
    '#10B981',
    '#F59E0B',
    '#8B5CF6',
    '#EC4899',
    '#06B6D4',
    '#84CC16',
    '#F97316',
    '#6366F1',
  ],
  cybersecurity: [
    '#dc2626', // red for critical
    '#f59e0b', // amber for high
    '#eab308', // yellow for medium
    '#22c55e', // green for low
    '#3b82f6', // blue for info
  ],
  status: {
    success: '#22c55e',
    warning: '#f59e0b',
    danger: '#dc2626',
    info: '#3b82f6',
    secondary: '#6b7280',
  },
} as const;

/**
 * Gets an array of colors for charts based on count needed
 */
export function getChartColors(
  count: number,
  palette: 'default' | 'cybersecurity' = 'default',
): string[] {
  const colors = CHART_COLOR_PALETTES[palette];
  const result: string[] = [];

  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }

  return result;
}

/**
 * Gets color for status/severity levels
 */
export function getStatusColor(status: string): string {
  const normalized = status.toLowerCase();

  switch (normalized) {
    case 'critical':
    case 'high':
    case 'danger':
    case 'error':
      return CHART_COLOR_PALETTES.status.danger;

    case 'warning':
    case 'medium':
    case 'warn':
      return CHART_COLOR_PALETTES.status.warning;

    case 'success':
    case 'low':
    case 'ok':
    case 'healthy':
      return CHART_COLOR_PALETTES.status.success;

    case 'info':
    case 'information':
      return CHART_COLOR_PALETTES.status.info;

    default:
      return CHART_COLOR_PALETTES.status.secondary;
  }
}

/**
 * Aggregates time series data points into fixed intervals (e.g., 5 minutes) using average.
 */
export function aggregateTimeSeries(
  data: Record<string, number>,
  intervalMs: number = 5 * 60 * 1000 // 5 minutes
): { x: Date; y: number }[] {
  if (!data) return [];
  const entries = Object.entries(data)
    .map(([ts, y]) => [Number(ts), y] as [number, number])
    .sort((a, b) => a[0] - b[0]);
  if (entries.length === 0) return [];

  const result: { x: Date; y: number }[] = [];
  let bucketStart = entries[0][0] - (entries[0][0] % intervalMs);
  let bucketEnd = bucketStart + intervalMs;
  let bucketVals: number[] = [];

  for (const [ts, y] of entries) {
    while (ts >= bucketEnd) {
      if (bucketVals.length > 0) {
        result.push({
          x: new Date(bucketStart),
          y: bucketVals.reduce((a, b) => a + b, 0) / bucketVals.length,
        });
      }
      bucketStart = bucketEnd;
      bucketEnd += intervalMs;
      bucketVals = [];
    }
    bucketVals.push(y);
  }
  if (bucketVals.length > 0) {
    result.push({
      x: new Date(bucketStart),
      y: bucketVals.reduce((a, b) => a + b, 0) / bucketVals.length,
    });
  }
  return result;
}

/**
 * Transforms raw data into chart-ready format
 */
export function transformForChart<T extends Record<string, any>>(
  data: T[],
  options: ChartTransformOptions = {},
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
    groupBy,
  } = options;

  if (!data.length) return [];

  // Group data if groupBy is specified
  if (groupBy) {
    return transformGroupedData(data, groupBy, options);
  }

  // Simple transformation for single dataset
  const colors = getChartColors(1);

  const chartData = data
    .map((item, index) => ({
      x: item.timestamp ? new Date(item.timestamp) : new Date(),
      y: typeof item.value === 'number' ? item.value : 0,
      ...item,
    }))
    .filter((entry) => {
      if (!filterInvalid) return true;
      return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
    });

  if (sortByTime) {
    chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
  }

  // Limit data points
  const limitedData = chartData.slice(-maxDataPoints);

  return [
    {
      label: 'Data',
      color: colors[0],
      data: limitedData,
    },
  ];
}

/**
 * Transforms grouped data into multiple chart datasets
 */
function transformGroupedData<T extends Record<string, any>>(
  data: T[],
  groupBy: string,
  options: ChartTransformOptions,
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
  } = options;

  // Group data by the specified field
  const groups = data.reduce(
    (acc, item) => {
      const groupKey = item[groupBy] || 'unknown';
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(item);
      return acc;
    },
    {} as Record<string, T[]>,
  );

  const colors = getChartColors(Object.keys(groups).length);

  return Object.entries(groups)
    .map(([groupKey, groupData], index) => {
      const chartData = groupData
        .map((item) => ({
          x: item.timestamp ? new Date(item.timestamp) : new Date(),
          y: typeof item.value === 'number' ? item.value : 0,
          ...item,
        }))
        .filter((entry) => {
          if (!filterInvalid) return true;
          return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
        });

      if (sortByTime) {
        chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
      }

      // Limit data points
      const limitedData = chartData.slice(-maxDataPoints);

      return {
        label: groupKey,
        color: colors[index],
        data: limitedData,
      };
    })
    .filter((dataset) => dataset.data.length > 0);
}

/**
 * Transforms deployment data specifically (matching the settings page pattern)
 * Handles both legacy format and modern format with timeseries/predictions
 */
export function transformDeploymentData(
  deployments: Array<{
    name: string;
    namespace: string;
    data: Record<string, number> | {
      timeseries: Record<string, number>;
      predictions: {
        "1min": Record<string, number>;
      };
      alerts: number[];
    };
    display_name?: string;
  }>,
  options?: {
    maxPoints?: number;
    minIntervalMs?: number;
    maxTimestamp?: number; // new option: only include data up to this timestamp
  }
): Record<string, ChartDataset[]> {
  if (!deployments.length) return {};

  const colors = getChartColors(20);
  const result: Record<string, ChartDataset[]> = {};
  const maxPoints = options?.maxPoints ?? 60;
  const minIntervalMs = options?.minIntervalMs ?? 60 * 1000; // 1 min minimum
  const maxTimestamp = options?.maxTimestamp;

  function filterByMaxTimestamp(entries: [number, number][]): [number, number][] {
    if (typeof maxTimestamp !== 'number') return entries;
    return entries.filter(([ts]) => ts <= maxTimestamp);
  }

  deployments.forEach((deployment, depIdx) => {
    const deploymentKey = `${deployment.name}-${deployment.namespace}`;
    const chartData: ChartDataset[] = [];
    const isModernFormat = deployment.data &&
      typeof deployment.data === 'object' &&
      'timeseries' in deployment.data &&
      'predictions' in deployment.data;

    if (isModernFormat) {
      const modernData = deployment.data as {
        timeseries: Record<string, number>;
        predictions: {
          "1min": Record<string, number>;
        };
        alerts: number[];
      };

      // Timeseries (strictly last N points, up to maxTimestamp)
      const timeseries = modernData.timeseries;
      if (timeseries && typeof timeseries === 'object' && Object.keys(timeseries).length > 0) {
        let entries = Object.entries(timeseries)
          .map(([ts, y]) => [Number(ts), y] as [number, number])
          .sort((a, b) => a[0] - b[0]);
        entries = filterByMaxTimestamp(entries);
        const lastN = entries.slice(-maxPoints).map(([ts, y]) => ({ x: new Date(ts), y }));
        if (lastN.length > 0) {
          chartData.push({
            label: deployment.display_name || deployment.name,
            color: colors[depIdx % colors.length],
            data: lastN,
          });
        }
      }
      // Predictions (1min, strictly last N points, up to maxTimestamp)
      const pred1min = modernData.predictions && modernData.predictions['1min'];
      if (pred1min && typeof pred1min === 'object' && Object.keys(pred1min).length > 0) {
        let entries = Object.entries(pred1min)
          .map(([ts, y]) => [Number(ts), y] as [number, number])
          .sort((a, b) => a[0] - b[0]);
        entries = filterByMaxTimestamp(entries);
        const lastN = entries.slice(-maxPoints).map(([ts, y]) => ({ x: new Date(ts), y }));
        if (lastN.length > 0) {
          chartData.push({
            label: (deployment.display_name || deployment.name) + ' (1min prediction)',
            color: colors[(depIdx + 1) % colors.length],
            data: lastN,
          });
        }
      }
    } else {
      // Legacy format (strictly last N points, up to maxTimestamp)
      const legacyData = deployment.data as Record<string, number>;
      if (legacyData && Object.keys(legacyData).length > 0) {
        let entries = Object.entries(legacyData)
          .map(([ts, y]) => [Number(ts), y] as [number, number])
          .sort((a, b) => a[0] - b[0]);
        entries = filterByMaxTimestamp(entries);
        const lastN = entries.slice(-maxPoints).map(([ts, y]) => ({ x: new Date(ts), y }));
        if (lastN.length > 0) {
          chartData.push({
            label: deployment.display_name || deployment.name,
            color: colors[depIdx % colors.length],
            data: lastN,
          });
        }
      }
    }

    if (chartData.length > 0) {
      result[deploymentKey] = chartData;
    }
  });

  return result;
}