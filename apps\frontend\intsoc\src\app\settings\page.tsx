'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>nt,
  <PERSON><PERSON>,
  Al<PERSON>,
  AlertDescription,
} from '@telesoft/ui';
import { MultiLineChart } from '@telesoft/d3';
import {
  useDeploymentsWebSocket,
  useDeploymentFiltersConfig,
} from '../../lib/hooks/useDeployments';
import {
  TabGroup,
  ConnectionStatus,
  StatsCard,
  Toggle,
} from '@telesoft/react-components';
import {
  NumberField,
  SelectField,
  FormSection,
  TextField,
} from '@telesoft/forms';
import {
  transformDeploymentData,
  STYLE_PRESETS,
  classNames,
} from '@telesoft/utils';
import Modal from '../../components/Modal';

// Reusable form field components are now imported from @telesoft/forms

export default function SettingsPage() {
  // Deployment mode state: { [namespace]: 'training' | 'live' }
  const [deploymentModes, setDeploymentModes] = useState<Record<string, 'training' | 'live'>>({});
  const [activeTab, setActiveTab] = useState<
    'datasources' | 'machinelearning' | 'ml'
  >('datasources');

  // Get deployments data for charts
  const {
    deployments,
    isConnected: isDeploymentsConnected,
    error: deploymentsError,
    lastUpdate,
  } = useDeploymentsWebSocket({
    autoConnect: true,
    fallbackToRest: true,
  });

  // Get deployment filters configuration
  const { filtersConfig, refresh: refreshFiltersConfig } =
    useDeploymentFiltersConfig();

  // Machine Learning Settings State
  const [mlSettings, setMlSettings] = useState({
    modelAccuracyThreshold: 85,
    trainingDataSize: 10000,
    autoRetrain: true,
    anomalyDetectionSensitivity: 'medium',
    threatScoringModel: 'ensemble',
    enableRealtimeProcessing: true,
    maxModelMemory: 4096,
    gpuAcceleration: false,
  });

  // Data Sources Settings State
  const [dataSourceSettings, setDataSourceSettings] = useState({
    logRetentionDays: 90,
    realTimeIngestion: true,
    compressionEnabled: true,
    encryptionAtRest: true,
    maxIngestionRate: 10000,
    dataValidation: true,
    sourceTimeout: 30,
    batchSize: 1000,
  });

  const [saveStatus, setSaveStatus] = useState<
    'idle' | 'saving' | 'saved' | 'error'
  >('idle');

  const [isAddDeploymentModalOpen, setIsAddDeploymentModalOpen] =
    useState(false);
  const [deploymentForm, setDeploymentForm] = useState({
    name: '',
    type: '',
    sourceField: '',
    filterType: '',
    filterValue: '',
    sourceMetric: '', // New field for Source Metric
  });
  const [deploymentSubmitStatus, setDeploymentSubmitStatus] = useState<
    'idle' | 'submitting' | 'success' | 'error'
  >('idle');

  // Delete mode state
  const [isDeleteMode, setIsDeleteMode] = useState(false);
  const [selectedDeployments, setSelectedDeployments] = useState<Set<string>>(
    new Set(),
  );
  const [deleteStatus, setDeleteStatus] = useState<
    'idle' | 'deleting' | 'success' | 'error'
  >('idle');

  // Deployment creation success state for main page
  const [deploymentCreateStatus, setDeploymentCreateStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');

  // State for managing pending deployments (placeholders)
  const [pendingDeployments, setPendingDeployments] = useState<
    Array<{
      id: string;
      name: string;
      namespace: string;
      timestamp: number;
    }>
  >([]);

  // Reorder mode state
  const [isReorderMode, setIsReorderMode] = useState(false);
  const [draggedNamespace, setDraggedNamespace] = useState<string | null>(null);
  const [dragOverNamespace, setDragOverNamespace] = useState<string | null>(
    null,
  );
  const [customOrder, setCustomOrder] = useState<string[]>([]);

  // Deployment type options
  const deploymentTypeOptions = [
    { value: 'spike', label: 'Anomaly Detection' },
    { value: 'dga', label: 'DGA' },
    { value: 'custom', label: 'Custom' },
  ];

  // Transform deployments data for charts grouped by namespace
  const chartDataByNamespace = React.useMemo(() => {
    return transformDeploymentData(deployments);
  }, [deployments]);

  // Helper function to get ordered namespaces
  const getOrderedNamespaces = React.useCallback(() => {
    const allNamespaces = Object.keys(chartDataByNamespace);
    const dgaOnlyNamespaces = deployments
      .filter((d) => d.name === 'dga')
      .map((d) => d.namespace)
      .filter((ns) => !allNamespaces.includes(ns));

    const allAvailableNamespaces = [...allNamespaces, ...dgaOnlyNamespaces];

    if (customOrder.length === 0) {
      return allAvailableNamespaces;
    }

    // Combine saved order with any new namespaces
    const orderedNamespaces = customOrder.filter((ns) =>
      allAvailableNamespaces.includes(ns),
    );
    const newNamespaces = allAvailableNamespaces.filter(
      (ns) => !customOrder.includes(ns),
    );

    return [...orderedNamespaces, ...newNamespaces];
  }, [chartDataByNamespace, deployments, customOrder]);

  // Load saved order from localStorage on mount
  React.useEffect(() => {
    const savedOrder = localStorage.getItem('deployment-card-order');
    if (savedOrder) {
      try {
        const parsedOrder = JSON.parse(savedOrder);
        if (Array.isArray(parsedOrder)) {
          setCustomOrder(parsedOrder);
        }
      } catch (error) {
        console.warn('Failed to parse saved deployment order:', error);
      }
    }
  }, []);

  // Save order to localStorage whenever it changes
  React.useEffect(() => {
    if (customOrder.length > 0) {
      localStorage.setItem(
        'deployment-card-order',
        JSON.stringify(customOrder),
      );
    }
  }, [customOrder]);

  // Update custom order when deployments change (new deployments added)
  React.useEffect(() => {
    if (deployments.length > 0) {
      const currentNamespaces = getOrderedNamespaces();
      const existingOrderedNamespaces = customOrder.filter((ns) =>
        currentNamespaces.includes(ns),
      );
      const newNamespaces = currentNamespaces.filter(
        (ns) => !customOrder.includes(ns),
      );

      if (newNamespaces.length > 0) {
        setCustomOrder((_prev) => [
          ...existingOrderedNamespaces,
          ...newNamespaces,
        ]);
      }
    }
  }, [deployments, getOrderedNamespaces, customOrder]);

  // Initialize deployment modes for existing deployments (default to Live mode)
  // and set new deployments to Training mode when they are created
  React.useEffect(() => {
    if (deployments.length > 0) {
      setDeploymentModes((prev) => {
        const updated = { ...prev };
        let hasChanges = false;

        deployments.forEach((deployment) => {
          const namespace = deployment.namespace;
          // If this deployment doesn't have a mode set yet, default to Live mode
          // (this handles existing deployments that should default to Live)
          if (!(namespace in updated)) {
            updated[namespace] = 'live';
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    }
  }, [deployments]);

  // Clean up stale pending deployments (older than 30 seconds)
  React.useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      setPendingDeployments((_prev) =>
        _prev.filter((pending) => now - pending.timestamp < 30000),
      );
    }, 5000); // Check every 5 seconds

    return () => clearInterval(cleanupInterval);
  }, []);

  // Remove pending deployments when actual deployments appear with matching names
  React.useEffect(() => {
    if (deployments.length > 0 && pendingDeployments.length > 0) {
      const deploymentNames = deployments.map((d) => d.name.toLowerCase());
      setPendingDeployments((prev) =>
        prev.filter(
          (pending) => !deploymentNames.includes(pending.name.toLowerCase()),
        ),
      );
    }
  }, [deployments, pendingDeployments]);

  // Helper function to extract DGA metrics from deployment data
  const getDGAMetrics = (namespaceDeployments: typeof deployments) => {
    // Find the DGA deployment in this namespace
    const dgaDeployment = namespaceDeployments.find((d) => d.name === 'dga');
    if (!dgaDeployment || !dgaDeployment.data) return null;

    // DGA deployments have a special data structure: {"domains_checked":8431,"dga_found":48,"inconclusive":7}
    const data = dgaDeployment.data;

    // Check if this is DGA-style data structure
    if (
      typeof data === 'object' &&
      'domains_checked' in data &&
      'dga_found' in data &&
      'inconclusive' in data
    ) {
      return {
        domainsChecked: data.domains_checked || 0,
        dgaFound: data.dga_found || 0,
        inconclusive: data.inconclusive || 0,
      };
    }

    return null;
  };



  const getFilterInfoForDeployment = (
    name: string,
    namespace: string,
  ): { key: string; value: string } => {


    if (!filtersConfig || !Array.isArray(filtersConfig)) {
      return { key: 'namespace', value: namespace };
    }

    const filterItem = filtersConfig.find(
      (item: any) => item.name === name && item.namespace === namespace,
    );


    if (filterItem?.FILTER_KEY && filterItem?.FILTER_VALUE) {
      return {
        key: filterItem.FILTER_KEY,
        value: filterItem.FILTER_VALUE,
      };
    }

    return { key: 'namespace', value: namespace };
  };

  // Helper function to get filter info for all deployments in a namespace
  const getFilterInfoForNamespace = (
    namespaceDeploys: typeof deployments,
  ): string[] => {
    if (namespaceDeploys.length === 0) return ['No deployments'];

    // Get all unique filter info for this namespace
    const filterInfos = namespaceDeploys.map((deploy) => {
      const info = getFilterInfoForDeployment(deploy.name, deploy.namespace);
      return `${info.key}: ${info.value}`;
    });

    // Remove duplicates
    return [...new Set(filterInfos)];
  };

  const handleSaveSettings = async () => {
    setSaveStatus('saving');
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (_error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  // Modal handlers for adding deployment
  const handleOpenAddDeploymentModal = () => {
    setIsAddDeploymentModalOpen(true);
    setDeploymentForm({
      name: '',
      type: '',
      sourceField: '',
      filterType: '',
      filterValue: '',
      sourceMetric: '', // New field for Source Metric
    });
    setDeploymentSubmitStatus('idle');
  };

  const handleCloseAddDeploymentModal = () => {
    setIsAddDeploymentModalOpen(false);
    setDeploymentForm({
      name: '',
      type: '',
      sourceField: '',
      filterType: '',
      filterValue: '',
      sourceMetric: '', // New field for Source Metric
    });
    setDeploymentSubmitStatus('idle');
  };

  const handleDeploymentFormChange = (field: string, value: string) => {
    setDeploymentForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCreateDeployment = async () => {
    if (!deploymentForm.name.trim() || !deploymentForm.type) {
      return; // Form validation would go here
    }

    setDeploymentSubmitStatus('submitting');

    // Add a placeholder deployment immediately
    const placeholderDeployment = {
      id: `pending-${Date.now()}`,
      name: deploymentForm.name.trim(),
      namespace: `pending-${deploymentForm.type}-${Date.now()}`, // Unique temporary namespace
      timestamp: Date.now(),
    };
    setPendingDeployments((prev) => [...prev, placeholderDeployment]);

    // Track the start time to ensure minimum 3 second display
    const startTime = Date.now();
    const minDisplayTime = 3000; // 3 seconds

    try {
      const response = await fetch('/api/v1/machine-learning/deployments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: deploymentForm.name.trim(),
          type: deploymentForm.type,
          sourceField: deploymentForm.sourceField,
          filterType: deploymentForm.filterType,
          filterValue: deploymentForm.filterValue,
          COUNT_TYPE: deploymentForm.sourceMetric || '',
          ...(deploymentForm.type === 'spike' && deploymentForm.sourceMetric
            ? {
              env: {
                consumer: {
                  COUNT_TYPE: deploymentForm.sourceMetric,
                },
              },
            }
            : {}),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Deployment created successfully:', result);

      // Set new deployment to Training mode (as per user preference)
      if (result.namespace) {
        setDeploymentModes((prev) => ({
          ...prev,
          [result.namespace]: 'training',
        }));
      }

      // Close modal immediately and show success on main page
      handleCloseAddDeploymentModal();
      setDeploymentCreateStatus('success');
      setTimeout(() => setDeploymentCreateStatus('idle'), 3000);

      // Refresh the filter config after successful deployment creation
      try {
        await refreshFiltersConfig();
        console.log('Filter config refreshed after deployment creation');

        // Calculate remaining time to ensure minimum display duration
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        // Remove the placeholder after minimum display time
        setTimeout(() => {
          setPendingDeployments((prev) =>
            prev.filter((p) => p.id !== placeholderDeployment.id),
          );
        }, remainingTime);
      } catch (filterError) {
        console.error('Failed to refresh filter config:', filterError);

        // Calculate remaining time to ensure minimum display duration even on error
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        // Still remove the placeholder after minimum display time
        setTimeout(() => {
          setPendingDeployments((prev) =>
            prev.filter((p) => p.id !== placeholderDeployment.id),
          );
        }, remainingTime);
      }
    } catch (error) {
      console.error('Failed to create deployment:', error);
      setDeploymentSubmitStatus('error');
      setTimeout(() => setDeploymentSubmitStatus('idle'), 3000);

      // Calculate remaining time to ensure minimum display duration even on deployment error
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

      // Remove the placeholder deployment after minimum display time
      setTimeout(() => {
        setPendingDeployments((prev) =>
          prev.filter((p) => p.id !== placeholderDeployment.id),
        );
      }, remainingTime);
    }
  };

  // Delete mode handlers
  const handleEnterDeleteMode = () => {
    setIsDeleteMode(true);
    setIsReorderMode(false); // Exit reorder mode when entering delete mode
    setSelectedDeployments(new Set());
    setDeleteStatus('idle');
  };

  const handleExitDeleteMode = () => {
    setIsDeleteMode(false);
    setSelectedDeployments(new Set());
    setDeleteStatus('idle');
  };

  const handleToggleDeploymentSelection = (namespace: string) => {
    setSelectedDeployments((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(namespace)) {
        newSet.delete(namespace);
      } else {
        newSet.add(namespace);
      }
      return newSet;
    });
  };

  const handleDeleteSelectedDeployments = async () => {
    if (selectedDeployments.size === 0) return;

    setDeleteStatus('deleting');

    try {
      // Build deployments array for the delete request
      const deploymentsToDelete = Array.from(selectedDeployments).map(
        (namespace) => {
          // Find the deployment with this namespace to get its type and name
          const deployment = deployments.find((d) => d.namespace === namespace);
          if (!deployment) {
            throw new Error(
              `Could not find deployment data for namespace: ${namespace}`,
            );
          }

          return {
            type: deployment.name as 'spike' | 'dga', // deployment.name maps to type
            namespace: deployment.namespace,
          };
        },
      );

      console.log('Deleting deployments:', deploymentsToDelete);

      // Call the DELETE endpoint
      const response = await fetch('/api/v1/machine-learning/deployments', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deployments: deploymentsToDelete,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`,
        );
      }

      const result = await response.json();
      console.log('Delete result:', result);

      // Check if there were any errors in the response
      if (result.errors && result.errors.length > 0) {
        console.warn('Some deletions failed:', result.errors);
        // Still show success if at least some were deleted
        if (result.deletedCount > 0) {
          setDeleteStatus('success');
        } else {
          setDeleteStatus('error');
          setTimeout(() => setDeleteStatus('idle'), 3000);
          return;
        }
      } else {
        setDeleteStatus('success');
      }

      setTimeout(() => {
        handleExitDeleteMode();
      }, 1500);
    } catch (error) {
      console.error('Failed to delete deployments:', error);
      setDeleteStatus('error');
      setTimeout(() => setDeleteStatus('idle'), 3000);
    }
  };

  // Reorder mode handlers
  const handleEnterReorderMode = () => {
    setIsReorderMode(true);
    setIsDeleteMode(false); // Exit delete mode when entering reorder mode
    setDraggedNamespace(null);
    // If we don't have a custom order yet, initialize it with current order
    if (customOrder.length === 0) {
      const currentOrder = getOrderedNamespaces();
      setCustomOrder(currentOrder);
    }
  };

  const handleExitReorderMode = () => {
    setIsReorderMode(false);
    setDraggedNamespace(null);
    setDragOverNamespace(null);
    // Reset cursor when exiting reorder mode
    document.body.style.cursor = '';
  };

  const handleDragStart = (namespace: string) => {
    if (!isReorderMode) return;
    setDraggedNamespace(namespace);
    setDragOverNamespace(null);
    // Add a small delay to prevent immediate visual feedback
    setTimeout(() => {
      document.body.style.cursor = 'grabbing';
    }, 0);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!isReorderMode) return;
    e.preventDefault();
  };

  const handleDragEnter = (namespace: string) => {
    if (!isReorderMode || draggedNamespace === namespace) return;
    setDragOverNamespace(namespace);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!isReorderMode) return;
    // Only clear drag over if we're actually leaving the element

    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverNamespace(null);
    }
  };

  const handleDragEnd = () => {
    if (!isReorderMode) return;
    // Reset cursor and clean up state
    document.body.style.cursor = '';
    setDraggedNamespace(null);
    setDragOverNamespace(null);
  };

  const handleDrop = (e: React.DragEvent, targetNamespace: string) => {
    e.preventDefault();

    // Reset cursor
    document.body.style.cursor = '';

    if (
      !isReorderMode ||
      !draggedNamespace ||
      draggedNamespace === targetNamespace
    ) {
      setDragOverNamespace(null);
      return;
    }

    setCustomOrder((prev) => {
      const newOrder = [...prev];
      const draggedIndex = newOrder.indexOf(draggedNamespace);
      const targetIndex = newOrder.indexOf(targetNamespace);

      if (draggedIndex === -1 || targetIndex === -1) {
        return prev;
      }

      // Remove dragged item and insert at target position
      newOrder.splice(draggedIndex, 1);
      newOrder.splice(targetIndex, 0, draggedNamespace);

      return newOrder;
    });

    setDraggedNamespace(null);
    setDragOverNamespace(null);
  };

  const [editDeploymentNamespace, setEditDeploymentNamespace] = useState<string | null>(null);

  return (
    <div className={classNames(STYLE_PRESETS.pageContainer)}>
      <div className={classNames(STYLE_PRESETS.contentContainer)}>
        {/* Save Status Alert */}
        {saveStatus !== 'idle' && (
          <div className="mb-6">
            {saveStatus === 'saving' && (
              <Alert variant="info">
                <AlertDescription>Saving settings...</AlertDescription>
              </Alert>
            )}
            {saveStatus === 'saved' && (
              <Alert variant="success">
                <AlertDescription>
                  Settings saved successfully!
                </AlertDescription>
              </Alert>
            )}
            {saveStatus === 'error' && (
              <Alert variant="danger">
                <AlertDescription>
                  Failed to save settings. Please try again.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Main Settings Card */}
        <Card className="shadow-lg">
          <CardHeader>
            {/* Tab Navigation */}
            <TabGroup
              tabs={[
                { id: 'datasources', label: 'Data Sources' },
                { id: 'machinelearning', label: 'Machine Learning' },
                { id: 'ml', label: 'Model' },
              ]}
              activeTab={activeTab}
              onTabChange={(tabId: string) =>
                setActiveTab(tabId as 'datasources' | 'machinelearning' | 'ml')
              }
            />
          </CardHeader>



          <CardContent className="p-6">
            {/* Machine Learning Settings (Charts) */}
            {activeTab === 'machinelearning' && (
              <div className="space-y-4">
                {/* Error Display */}
                {deploymentsError && (
                  <Alert variant="danger" className="mb-4">
                    <AlertDescription>
                      <strong>Deployment Data Error:</strong> {deploymentsError}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Deployment Summary */}
                <div>
                  <div className="flex justify-between items-center">
                    <div className="w-1/3">
                      <h3 className="text-lg font-semibold text-text-primary">
                        Overview
                      </h3>
                    </div>
                    <div className="w-2/3 flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-text-primary">
                        Deployments
                      </h3>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        {isDeleteMode ? (
                          // Delete mode controls
                          <>
                            <span className="text-sm text-text-secondary mr-2">
                              {selectedDeployments.size} selected
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleExitDeleteMode}
                              disabled={deleteStatus === 'deleting'}
                            >
                              Cancel
                            </Button>
                            <Button
                              variant="danger"
                              size="sm"
                              onClick={handleDeleteSelectedDeployments}
                              loading={deleteStatus === 'deleting'}
                              disabled={
                                deleteStatus === 'deleting' ||
                                selectedDeployments.size === 0
                              }
                            >
                              {deleteStatus === 'deleting'
                                ? 'Deleting...'
                                : `Delete ${selectedDeployments.size > 0 ? `(${selectedDeployments.size})` : ''}`}
                            </Button>
                          </>
                        ) : isReorderMode ? (
                          // Reorder mode controls
                          <>
                            <span className="text-sm text-text-secondary mr-2">
                              Drag cards to reorder
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleExitReorderMode}
                            >
                              Done
                            </Button>
                          </>
                        ) : (
                          // Normal mode controls
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleEnterReorderMode}
                              disabled={deployments.length === 0}
                            >
                              <svg
                                className="w-4 h-4 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M4 6h16M4 10h16M4 14h16M4 18h16"
                                />
                              </svg>
                              Reorder
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleEnterDeleteMode}
                              disabled={deployments.length === 0}
                            >
                              <svg
                                className="w-4 h-4 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9m1 1v1h4V4z"
                                />
                              </svg>
                              Delete
                            </Button>
                            <Button
                              variant="modern"
                              size="sm"
                              onClick={handleOpenAddDeploymentModal}
                            >
                              <span className="text-base">+</span>
                              Add Model
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Delete mode status messages */}
                  {isDeleteMode && (
                    <div className="mt-3">
                      {deleteStatus === 'success' && (
                        <Alert variant="success">
                          <AlertDescription>
                            Selected deployments deleted successfully!
                          </AlertDescription>
                        </Alert>
                      )}
                      {deleteStatus === 'error' && (
                        <Alert variant="danger">
                          <AlertDescription>
                            Failed to delete deployments. Please try again.
                          </AlertDescription>
                        </Alert>
                      )}
                      {deleteStatus === 'idle' && (
                        <Alert variant="info">
                          <AlertDescription>
                            Click on deployment cards to select them for
                            deletion.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}

                  {/* Reorder mode status messages */}
                  {isReorderMode && (
                    <div className="mt-3">
                      <Alert variant="info">
                        <AlertDescription>
                          Drag and drop cards to reorder them. Changes are saved
                          automatically.
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {/* Deployment creation success message */}
                  {deploymentCreateStatus === 'success' && (
                    <div className="mt-3">
                      <Alert variant="success">
                        <AlertDescription>
                          Deployment created successfully!
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {deploymentCreateStatus === 'error' && (
                    <div className="mt-3">
                      <Alert variant="danger">
                        <AlertDescription>
                          Failed to create deployment. Please try again.
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}
                </div>

                {/* Main Content Layout - Left sidebar and Right chart */}
                <div className="flex gap-6 min-h-[600px]">
                  {/* Left Sidebar - Summary and Controls (1/3 width) */}
                  <div className="w-1/3 space-y-6">
                    {/* Summary Cards */}
                    <div className="space-y-4">
                      <Card>
                        <CardContent className="p-4">
                          <ConnectionStatus
                            isConnected={isDeploymentsConnected}
                            lastUpdate={lastUpdate}
                            label="Connection Status"
                            showLastUpdate={true}
                          />
                        </CardContent>
                      </Card>
                      {deployments.length > 0 && (
                        <>
                          <StatsCard
                            title="Active Deployments"
                            value={deployments.length}
                            variant="info"
                          />
                          <StatsCard
                            title="Total Data Points"
                            value={deployments.reduce(
                              (sum, d) => sum + Object.keys(d.data).length,
                              0,
                            )}
                            variant="default"
                          />
                          <StatsCard
                            title="Active Charts"
                            value={Object.keys(chartDataByNamespace).length}
                            variant="success"
                          />
                        </>
                      )}
                    </div>
                  </div>

                  {/* Right Chart Area (2/3 width) */}
                  <div className="w-2/3">
                    {Object.keys(chartDataByNamespace).length > 0 ||
                      deployments.some((d) => d.name === 'dga') ? (
                      <div className="space-y-4">
                        {/* Show placeholder cards if any exist - they hide all other deployments */}
                        {pendingDeployments.length > 0
                          ? pendingDeployments.map((pendingDeployment) => (
                            <Card
                              key={pendingDeployment.id}
                              className="relative opacity-75 border-dashed border-2 border-blue-300"
                            >
                              <CardHeader className="pb-3">
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-3">
                                    <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                                    <div>
                                      <h3 className="text-base font-semibold text-text-primary">
                                        {pendingDeployment.name}
                                      </h3>
                                      <div className="text-xs text-text-secondary">
                                        <div>
                                          Status: Creating deployment...
                                        </div>
                                        <div className="ml-2">
                                          Fetching filter configuration...
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="text-blue-500">
                                    <svg
                                      className="w-5 h-5"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </CardHeader>
                              <CardContent className="pt-0">
                                <div className="w-full relative py-8 flex items-center justify-center">
                                  <div className="text-center">
                                    <div className="animate-pulse">
                                      <div className="w-32 h-4 bg-surface-secondary rounded mb-2 mx-auto"></div>

                                      <div className="w-24 h-3 bg-surface-secondary/70 rounded mx-auto"></div>
                                    </div>
                                    <div className="text-sm text-text-secondary mt-4">
                                      Initializing deployment...
                                    </div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))
                          : /* Render deployments in order - only when no placeholders exist */
                          getOrderedNamespaces()
                            .map((namespace) => {
                              // Get chart data if available
                              const chartData =
                                chartDataByNamespace[namespace];

                              // Find deployments for this namespace to get display names
                              const namespaceDeploys = deployments.filter(
                                (d) => d.namespace === namespace,
                              );

                              const displayNames = namespaceDeploys
                                .map((d) => d.display_name || d.name)
                                .join(', ');
                              const isSelected =
                                selectedDeployments.has(namespace);

                              // Check if this namespace contains DGA deployments
                              const dgaMetrics =
                                getDGAMetrics(namespaceDeploys);
                              const isDGA = dgaMetrics !== null;

                              // Skip if no deployments for this namespace
                              if (namespaceDeploys.length === 0) return null;

                              // Check if this card is being dragged over for reordering
                              const isDraggedOver =
                                isReorderMode &&
                                dragOverNamespace === namespace;
                              const isDraggedItem =
                                isReorderMode &&
                                draggedNamespace === namespace;

                              return (
                                <React.Fragment key={namespace}>
                                  {/* Drop zone indicator - show above the hovered card */}
                                  {isReorderMode &&
                                    isDraggedOver &&
                                    draggedNamespace &&
                                    draggedNamespace !== namespace && (
                                      <div className="relative">
                                        <div className="h-1 bg-blue-400 rounded-full mx-4 shadow-lg animate-pulse">
                                          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full shadow-lg">
                                            Drop here
                                          </div>
                                        </div>
                                      </div>
                                    )}

                                  <Card
                                    draggable={isReorderMode}
                                    onDragStart={() =>
                                      isReorderMode &&
                                      handleDragStart(namespace)
                                    }
                                    onDragEnd={handleDragEnd}
                                    onDragOver={handleDragOver}
                                    onDragEnter={() =>
                                      isReorderMode &&
                                      handleDragEnter(namespace)
                                    }
                                    onDragLeave={handleDragLeave}
                                    onDrop={(e) =>
                                      isReorderMode &&
                                      handleDrop(e, namespace)
                                    }
                                    className={classNames(
                                      'relative transition-all duration-200',
                                      isDeleteMode &&
                                      'cursor-pointer hover:shadow-md',
                                      isReorderMode &&
                                      !isDraggedItem &&
                                      'cursor-move hover:shadow-lg',
                                      isReorderMode &&
                                      !isDraggedItem &&
                                      'border-dashed border-2 border-border-secondary',

                                      isReorderMode &&
                                      isDraggedItem &&
                                      'opacity-50 scale-95 rotate-1 shadow-2xl z-50 cursor-grabbing',
                                      isReorderMode &&
                                      isDraggedOver &&
                                      !isDraggedItem &&
                                      'border-accent-primary bg-accent-primary/10 shadow-lg scale-[1.02]',
                                      isSelected &&
                                      'ring-2 ring-red-500/40 bg-red-500/10',
                                    )}
                                    onClick={
                                      isDeleteMode
                                        ? () =>
                                          handleToggleDeploymentSelection(
                                            namespace,
                                          )
                                        : undefined
                                    }
                                  >
                                    <CardHeader className="pb-3">
                                      <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-3">
                                          {/* Drag handle in reorder mode */}
                                          {isReorderMode && (
                                            <div className="flex items-center text-text-subtle cursor-move">
                                              <svg
                                                className="w-5 h-5"
                                                fill="currentColor"
                                                viewBox="0 0 20 20"
                                              >
                                                <path d="M7 2a1 1 0 00-1 1v2a1 1 0 102 0V4a1 1 0 00-1-1zM7 8a1 1 0 00-1 1v2a1 1 0 102 0V9a1 1 0 00-1-1zM7 14a1 1 0 00-1 1v2a1 1 0 102 0v-1a1 1 0 00-1-1zM13 2a1 1 0 00-1 1v2a1 1 0 102 0V4a1 1 0 00-1-1zM13 8a1 1 0 00-1 1v2a1 1 0 102 0V9a1 1 0 00-1-1zM13 14a1 1 0 00-1 1v2a1 1 0 102 0v-1a1 1 0 00-1-1z" />
                                              </svg>
                                            </div>
                                          )}

                                          {/* Selection checkbox in delete mode */}
                                          {isDeleteMode && (
                                            <div className="flex items-center">
                                              <input
                                                type="checkbox"
                                                checked={isSelected}
                                                onChange={() =>
                                                  handleToggleDeploymentSelection(
                                                    namespace,
                                                  )
                                                }
                                                className="w-4 h-4 text-red-600 border-border-primary rounded focus:ring-red-500"
                                                onClick={(e) =>
                                                  e.stopPropagation()
                                                }
                                              />
                                            </div>
                                          )}
                                          <div>
                                            <h3 className="text-base font-semibold text-text-primary">
                                              {displayNames || namespace}
                                            </h3>
                                            <div className="text-xs text-text-secondary">
                                              <div>Filter:</div>
                                              {getFilterInfoForNamespace(
                                                namespaceDeploys,
                                              ).map((filterInfo, idx) => (
                                                <div
                                                  key={idx}
                                                  className="ml-2"
                                                >
                                                  {filterInfo}
                                                </div>
                                              ))}
                                            </div>
                                          </div>
                                        </div>

                                        {/* Top right toggle button with state and status */}
                                        <div className="absolute top-3 right-3 z-20 flex items-center gap-2">
                                          <Toggle
                                            isActive={deploymentModes[namespace] === 'live'}
                                            onChange={() => {
                                              setDeploymentModes((prev) => ({
                                                ...prev,
                                                [namespace]: prev[namespace] === 'live' ? 'training' : 'live',
                                              }));
                                            }}
                                            label=""
                                            description=""
                                          />
                                          {/* Edit button */}
                                          <button
                                            type="button"
                                            className="px-2 py-1 border border-border-primary rounded text-xs font-semibold text-text-primary bg-transparent hover:bg-accent-primary/10 hover:text-accent-primary focus:outline-none focus:ring-2 focus:ring-accent-primary transition-colors"
                                            title="Edit deployment"
                                            onClick={() => setEditDeploymentNamespace(namespace)}
                                          >
                                            EDIT
                                          </button>
                                          <span
                                            className={
                                              deploymentModes[namespace] === 'live'
                                                ? 'bg-green-500/90 text-white text-xs font-semibold ml-1 px-3 py-1 rounded-full transition-colors duration-200'
                                                : 'bg-orange-400/90 text-white text-xs font-semibold ml-1 px-3 py-1 rounded-full transition-colors duration-200'
                                            }
                                            style={{
                                              display: 'inline-block',
                                              width: 120,
                                              minWidth: 120,
                                              maxWidth: 120,
                                              textAlign: 'center',
                                              whiteSpace: 'nowrap',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                            }}
                                          >
                                            Status: {deploymentModes[namespace] === 'live' ? 'Live' : 'Training'}
                                          </span>
                                        </div>
                                      </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                      <div className="w-full relative">
                                        {isDGA ? (
                                          // DGA Metrics Display
                                          <div className="grid grid-cols-3 gap-4 py-6">
                                            <div className="text-center">
                                              <div className="flex items-center justify-center mb-2">
                                                <div className="w-4 h-8 bg-blue-500 rounded-sm mr-1"></div>
                                                <div>
                                                  <div className="text-sm font-medium text-text-secondary">
                                                    All Domains Checked
                                                  </div>
                                                  <div className="text-2xl font-bold text-text-primary">
                                                    {dgaMetrics.domainsChecked.toLocaleString()}
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                            <div className="text-center">
                                              <div className="flex items-center justify-center mb-2">
                                                <div className="w-4 h-8 bg-red-500 rounded-sm mr-1"></div>
                                                <div>
                                                  <div className="text-sm font-medium text-text-secondary">
                                                    DGA Domains
                                                  </div>
                                                  <div className="text-2xl font-bold text-text-primary">
                                                    {dgaMetrics.dgaFound}
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                            <div className="text-center">
                                              <div className="flex items-center justify-center mb-2">
                                                <div className="w-4 h-8 bg-yellow-500 rounded-sm mr-1"></div>
                                                <div>
                                                  <div className="text-sm font-medium text-text-secondary">
                                                    Inconclusive
                                                  </div>
                                                  <div className="text-2xl font-bold text-text-primary">
                                                    {dgaMetrics.inconclusive}
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        ) : chartData ? (
                                          // Regular Chart Display for non-DGA deployments
                                          <MultiLineChart
                                            datasets={chartData as any}
                                            width={800}
                                            height={120}
                                            margin={{
                                              top: 15,
                                              right: 120,
                                              bottom: 30,
                                              left: 60,
                                            }}
                                            strokeWidth={1.5}
                                            showLegend={true}
                                            legendLocation="right"
                                            className="w-full"
                                          />

                                        ) : null}

                                        {/* Selection overlay in delete mode */}
                                        {isDeleteMode && isSelected && (
                                          <div className="absolute top-3 right-3 z-10">
                                            <div
                                              className={classNames(
                                                'backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm border transition-all duration-300',
                                                'bg-surface-primary/70 border-border-primary/30',
                                                'ring-1 ring-red-500/30 bg-red-500/20 border-red-500/60',
                                              )}
                                            >
                                              <span
                                                className={classNames(
                                                  'text-[10px] font-medium tracking-wider transition-colors duration-200 uppercase',
                                                  'text-red-600',
                                                )}
                                              >
                                                Selected
                                              </span>
                                            </div>
                                          </div>
                                        )}

                                        {/* Reorder overlay */}
                                        {isReorderMode && !isDraggedItem && (
                                          <div className="absolute top-3 right-3 z-10">
                                            <div
                                              className={classNames(
                                                'backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm border transition-all duration-300',
                                                isDraggedOver
                                                  ? 'bg-accent-primary/30 border-accent-primary/70'
                                                  : 'bg-accent-primary/20 border-accent-primary/60',
                                              )}
                                            >
                                              <span
                                                className={classNames(
                                                  'text-[10px] font-medium tracking-wider transition-colors duration-200 uppercase',
                                                  isDraggedOver
                                                    ? 'text-accent-primary'
                                                    : 'text-accent-primary/80',
                                                )}
                                              >
                                                {isDraggedOver
                                                  ? 'Drop here'
                                                  : 'Drag to reorder'}
                                              </span>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </CardContent>
                                  </Card>
                                </React.Fragment>
                              );
                            })
                            .filter(Boolean)}
                      </div>
                    ) : (
                      <Card className="h-full">
                        <CardHeader>
                          <div className="flex justify-between items-center">
                            <div>
                              <h3 className="text-lg font-semibold text-text-primary">
                                Deployment Metrics Over Time
                              </h3>
                              <p className="text-sm text-text-secondary">
                                No deployment data available
                              </p>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center justify-center min-h-[400px]">
                            <div className="text-center">
                              <div className="mb-6">
                                <svg
                                  className="w-16 h-16 mx-auto text-text-subtle/40"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={1.5}
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                  />
                                </svg>
                              </div>
                              <h4 className="text-lg font-medium text-text-primary mb-2">
                                {deployments.length === 0
                                  ? 'No Deployment Data'
                                  : 'Loading Chart...'}
                              </h4>
                              <p className="text-text-secondary mb-4">
                                {deployments.length === 0
                                  ? 'Connect to the ML service to view deployment metrics'
                                  : 'Preparing chart data...'}
                              </p>
                              {deploymentsError && (
                                <p className="text-sm text-red-500">
                                  Error: {deploymentsError}
                                </p>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              </div>
            )}


            {/* Machine Learning Settings */}
            {activeTab === 'ml' && (
              <div className="space-y-8">
                <div>
                  <h2 className="text-xl font-semibold text-text-primary mb-4">
                    Machine Learning Configuration
                  </h2>
                  <p className="text-text-secondary mb-6">
                    Configure AI models, training parameters, and threat
                    detection algorithms.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Model Performance */}
                  <FormSection
                    title="Model Performance"
                    icon="🎯"
                    description="Configure ML model performance parameters"
                    className=""
                  >
                    <NumberField
                      label="Model Accuracy Threshold (%)"
                      value={mlSettings.modelAccuracyThreshold}
                      onChange={(value: number) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          modelAccuracyThreshold: value,
                        }))
                      }
                      min="50"
                      max="100"
                      helperText="Minimum accuracy required for model deployment"
                      error=""
                      disabled={false}
                      placeholder=""
                      className=""
                    />

                    <NumberField
                      label="Training Data Size"
                      value={mlSettings.trainingDataSize}
                      onChange={(value) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          trainingDataSize: value,
                        }))
                      }
                      helperText="Number of samples for model training"
                    />

                    <SelectField
                      label="Threat Scoring Model"
                      value={mlSettings.threatScoringModel}
                      onChange={(value) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          threatScoringModel: value,
                        }))
                      }
                      options={[
                        { value: 'ensemble', label: 'Ensemble Model' },
                        { value: 'neural', label: 'Neural Network' },
                        { value: 'random-forest', label: 'Random Forest' },
                        { value: 'gradient-boost', label: 'Gradient Boosting' },
                      ]}
                    />
                  </FormSection>

                  {/* Detection Settings */}
                  <FormSection title="Detection Settings" icon="🔍">
                    <SelectField
                      label="Anomaly Detection Sensitivity"
                      value={mlSettings.anomalyDetectionSensitivity}
                      onChange={(value) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          anomalyDetectionSensitivity: value,
                        }))
                      }
                      options={[
                        { value: 'low', label: 'Low' },
                        { value: 'medium', label: 'Medium' },
                        { value: 'high', label: 'High' },
                        { value: 'ultra', label: 'Ultra High' },
                      ]}
                      helperText="Higher sensitivity may increase false positives"
                    />

                    <NumberField
                      label="Max Model Memory (MB)"
                      value={mlSettings.maxModelMemory}
                      onChange={(value) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          maxModelMemory: value,
                        }))
                      }
                      min="1024"
                      max="16384"
                    />

                    <Toggle
                      isActive={mlSettings.autoRetrain}
                      onChange={(value: boolean) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          autoRetrain: value,
                        }))
                      }
                      label="Auto-Retrain Models"
                      description="Automatically retrain with new data"
                    />

                    <Toggle
                      isActive={mlSettings.enableRealtimeProcessing}
                      onChange={(value: boolean) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          enableRealtimeProcessing: value,
                        }))
                      }
                      label="Real-time Processing"
                      description="Process threats in real-time"

                    />

                    <Toggle
                      isActive={mlSettings.gpuAcceleration}
                      onChange={(value: boolean) =>
                        setMlSettings((prev) => ({
                          ...prev,
                          gpuAcceleration: value,
                        }))
                      }
                      label="GPU Acceleration"
                      description="Use GPU for model inference"
                    />
                  </FormSection>
                </div>

                {/* Status Indicators */}
                <div className="border-t border-border-primary/20 pt-6">
                  <h3 className="text-lg font-medium text-text-primary mb-4">
                    Current Status
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    <Badge
                      variant={mlSettings.autoRetrain ? 'success' : 'secondary'}
                    >
                      Auto-retrain:{' '}
                      {mlSettings.autoRetrain ? 'Enabled' : 'Disabled'}
                    </Badge>
                    <Badge
                      variant={
                        mlSettings.enableRealtimeProcessing
                          ? 'success'
                          : 'warning'
                      }
                    >
                      Real-time:{' '}
                      {mlSettings.enableRealtimeProcessing
                        ? 'Active'
                        : 'Inactive'}
                    </Badge>
                    <Badge
                      variant={
                        mlSettings.gpuAcceleration ? 'success' : 'secondary'
                      }
                    >
                      GPU: {mlSettings.gpuAcceleration ? 'Enabled' : 'CPU Only'}
                    </Badge>
                    <Badge variant="default">
                      Accuracy: {mlSettings.modelAccuracyThreshold}%
                    </Badge>
                  </div>
                </div>
              </div>
            )}

            {/* Data Sources Settings */}
            {activeTab === 'datasources' && (
              <div className="space-y-6">
                {/* Header with Add button */}
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl font-semibold text-text-primary mb-2">
                      Data Sources Configuration
                    </h2>
                    <p className="text-text-secondary">
                      Configure data ingestion, storage, and processing parameters.
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Add
                  </Button>
                </div>

                {/* Network Probe Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    Network Probe
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      {/* Left side - Stats */}
                      <div className="text-center">
                        <div className="text-3xl font-bold text-text-primary">0.0</div>
                        <div className="text-sm text-text-secondary">bps</div>
                        <div className="text-xs text-text-secondary mt-1">MAC 4</div>
                      </div>

                      {/* Center - Device Image and Name with Status */}
                      <div className="flex flex-col items-center space-y-3">
                        <div className="text-center">
                          <div className="w-40 h-20 bg-gradient-to-r from-amber-600 to-amber-800 rounded-lg mb-2 flex items-center justify-center">
                            <div className="text-sm text-white font-mono">MPAC 6650</div>
                          </div>
                          <div className="text-sm font-medium text-text-primary">
                            Telesoft MPAC 6650
                          </div>
                        </div>

                        {/* Status indicators */}
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div className="text-sm text-green-600">Up 8s</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            <div className="text-sm text-text-secondary">Down 0s</div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <div className="text-sm text-text-secondary">Errors 0</div>
                          </div>
                        </div>
                      </div>

                      {/* Right side - Metrics and Actions */}
                      <div className="flex flex-col items-end space-y-4">
                        <div className="flex flex-col space-y-3">
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                            <div className="text-sm text-text-secondary">Active Flows</div>
                            <div className="text-lg font-semibold text-text-primary">100,000</div>
                          </div>
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                            <div className="text-sm text-text-secondary">Flows / s</div>
                            <div className="text-lg font-semibold text-text-primary">100,000</div>
                          </div>
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                            <div className="text-sm text-text-secondary">Expected Flows / s</div>
                            <div className="text-lg font-semibold text-text-primary">100,000</div>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Syslog Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    Syslog
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="text-lg font-medium text-text-primary">Messages / S</div>
                        <div className="text-2xl font-bold text-text-primary">100,000</div>
                      </div>

                      <div className="flex flex-col items-end space-y-4">
                        <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                          <div className="text-sm text-text-secondary">Listener Port</div>
                          <div className="text-lg font-semibold text-text-primary">514</div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* IPFIX Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    IPFIX
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="text-lg font-medium text-text-primary">Flows / S</div>
                        <div className="text-2xl font-bold text-text-primary">100,000</div>
                      </div>

                      <div className="flex flex-col items-end space-y-4">
                        <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                          <div className="text-sm text-text-secondary">Listener Port</div>
                          <div className="text-lg font-semibold text-text-primary">4739</div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Host Sensors Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    Host Sensors
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="text-lg font-medium text-text-primary">Active Sensors</div>
                        <div className="text-2xl font-bold text-text-primary">230</div>
                      </div>

                      <div className="flex flex-col items-end space-y-4">
                        <div className="flex flex-col space-y-3">
                          {/* Windows Box */}
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-5 py-3 flex items-center space-x-3 min-w-[140px]">
                            <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                              <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351" />
                              </svg>
                            </div>
                            <div>
                              <div className="text-sm text-text-secondary">Windows</div>
                              <div className="text-lg font-semibold text-text-primary">200</div>
                            </div>
                          </div>

                          {/* Linux Box */}
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-5 py-3 flex items-center space-x-3 min-w-[140px]">
                            <div className="w-6 h-6 bg-yellow-500 rounded flex items-center justify-center">
                              <svg className="w-4 h-4 text-black" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12.504 0c-.155 0-.315.008-.48.021-4.226.333-3.105 4.807-3.17 6.298-.076 1.092-.3 1.953-1.05 3.02-.885 1.051-2.127 2.75-2.716 4.521-.278.832-.41 1.684-.287 2.489a.424.424 0 00-.11.135c-.26.268-.45.6-.663.839-.199.199-.485.267-.797.4-.313.136-.658.269-.864.68-.09.189-.136.394-.132.602 0 .199.027.4.055.536.058.399.116.728.04.97-.249.68-.28 1.145-.106 1.484.174.334.535.47.94.601.81.2 1.91.135 2.774.6.926.466 1.866.67 2.616.47.526-.116.97-.464 1.208-.946.587-.003 1.23-.269 2.26-.334.699-.058 1.574.267 2.577.2.025.134.063.198.114.333l.003.003c.391.778 1.113 1.132 1.884 1.071.771-.06 1.592-.536 2.257-1.306.631-.765 1.683-1.084 2.378-1.503.348-.199.629-.469.649-.853.023-.4-.2-.811-.714-1.376v-.097l-.003-.003c-.17-.2-.25-.535-.338-.926-.085-.401-.182-.786-.492-1.046h-.003c-.059-.054-.123-.067-.188-.135a.357.357 0 00-.19-.064c.431-1.278.264-2.55-.173-3.694-.533-1.41-1.465-2.638-2.175-3.483-.796-1.005-1.576-1.957-1.56-3.368.026-2.152.236-6.133-3.544-6.139zm.529 3.405h.013c.213 0 .396.062.584.198.19.135.33.332.438.533.105.259.158.459.166.724 0-.02.006-.04.006-.06v.105a.086.086 0 01-.004-.021l-.004-.024a1.807 1.807 0 01-.15.706.953.953 0 01-.213.335.71.71 0 01-.088.066c-.297.168-.623.336-.985.328-.27-.005-.52-.165-.705-.405a1.528 1.528 0 01-.088-.667c.014-.245.027-.454.138-.664.138-.26.322-.465.556-.606.115-.071.259-.104.406-.104l.029-.005zm4.049.555c.013 0 .025.006.037.006.412.006.811.167 1.149.329.292.138.543.329.738.54.334.378.572.835.729 1.329.297.931.84 1.927 1.772 2.819 1.023 1.025 1.86 2.33 2.278 3.737.332 1.136.111 2.267-.302 3.284-.302.759-.75 1.475-1.302 2.117a.996.996 0 01-.028-.004c-.078-.016-.154-.016-.224-.045a1.17 1.17 0 01-.19-.09 1.039 1.039 0 01-.333-.333 2.584 2.584 0 00-.037-.067l-.004-.004c-.102-.199-.202-.335-.358-.406a.131.131 0 00-.018-.003c-.059-.024-.123-.024-.184-.06a1.696 1.696 0 01-.66-.66c-.091-.199-.185-.406-.226-.645-.243-.931-.359-1.924-.251-2.906.17-1.543.372-2.454.372-2.454s-.36 1.019-.732 2.574c-.3 1.25-.257 2.562-.085 3.85.058.437.156.858.27 1.28.09.334.177.67.213 1.018.144 1.397-.336 2.719-1.29 3.731-.954 1.013-2.244 1.68-3.532 1.68-1.288 0-2.578-.667-3.532-1.68-.954-1.012-1.434-2.334-1.29-3.731.036-.348.123-.684.213-1.018.114-.422.212-.843.27-1.28.172-1.288.215-2.6-.085-3.85-.372-1.555-.732-2.574-.732-2.574s.202.911.372 2.454c.108.982-.008 1.975-.251 2.906-.041.239-.135.446-.226.645a1.696 1.696 0 01-.66.66c-.061.036-.125.036-.184.06a.131.131 0 00-.018.003c-.156.071-.256.207-.358.406l-.004.004c-.013.022-.025.044-.037.067a1.039 1.039 0 01-.333.333c-.036.036-.075.067-.19.09-.07.029-.146.029-.224.045a.996.996 0 01-.028.004c-.552-.642-1-.358-1.302-2.117-.413-1.017-.634-2.148-.302-3.284.418-1.407 1.255-2.712 2.278-3.737.932-.892 1.475-1.888 1.772-2.819.157-.494.395-.951.729-1.329.195-.211.446-.402.738-.54.338-.162.737-.323 1.149-.329z" />
                              </svg>
                            </div>
                            <div>
                              <div className="text-sm text-text-secondary">Linux</div>
                              <div className="text-lg font-semibold text-text-primary">30</div>
                            </div>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            )}
          </CardContent>

          {/* Action Buttons - Only show on Model and Data Sources tabs */}
          {activeTab !== 'machinelearning' && (
            <div className="flex justify-end gap-4 p-6 border-t border-border-primary/20">
              <Button
                variant="outline"
                onClick={() => {
                  // Reset to default values
                  if (activeTab === 'ml') {
                    setMlSettings({
                      modelAccuracyThreshold: 85,
                      trainingDataSize: 10000,
                      autoRetrain: true,
                      anomalyDetectionSensitivity: 'medium',
                      threatScoringModel: 'ensemble',
                      enableRealtimeProcessing: true,
                      maxModelMemory: 4096,
                      gpuAcceleration: false,
                    });
                  } else if (activeTab === 'datasources') {
                    setDataSourceSettings({
                      logRetentionDays: 90,
                      realTimeIngestion: true,
                      compressionEnabled: true,
                      encryptionAtRest: true,
                      maxIngestionRate: 10000,
                      dataValidation: true,
                      sourceTimeout: 30,
                      batchSize: 1000,
                    });
                  }
                }}
              >
                Reset to Defaults
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveSettings}
                loading={saveStatus === 'saving'}
                disabled={saveStatus === 'saving'}
              >
                {saveStatus === 'saving' ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          )}
        </Card>
      </div>

      {/* Add Deployment Modal */}
      {isAddDeploymentModalOpen && (
        <Modal
          isOpen={isAddDeploymentModalOpen}
          onClose={handleCloseAddDeploymentModal}
          type="Add New Deployment"
        >
          <div className="p-6 space-y-6">
            <div>
              <p className="text-text-secondary text-sm">
                Create a new machine learning deployment model.
              </p>
            </div>

            {/* Form Fields */}
            <div className="space-y-4">
              <TextField
                label="Name"
                value={deploymentForm.name}
                onChange={(value) => handleDeploymentFormChange('name', value)}
                placeholder="Enter deployment name"
                error={
                  !deploymentForm.name.trim() &&
                    deploymentSubmitStatus === 'error'
                    ? 'Name is required'
                    : undefined
                }
              />

              <SelectField
                label="Type"
                value={deploymentForm.type}
                onChange={(value) => handleDeploymentFormChange('type', value)}
                options={deploymentTypeOptions}
                placeholder="Select deployment type"
                error={
                  !deploymentForm.type && deploymentSubmitStatus === 'error'
                    ? 'Type is required'
                    : undefined
                }
              />
            </div>
            {/* Model Configuration Panel */}
            {deploymentForm.type === 'spike' && (
              <div className="mt-2">
                <div className="mb-4">
                  <span className="block text-lg font-handwritten mb-2">Model Configuration</span>
                  <div className="flex flex-col gap-4 w-full">
                    <SelectField
                      label="Source Field:"
                      value={deploymentForm.sourceField || ''}
                      onChange={(value) => handleDeploymentFormChange('sourceField', value)}
                      options={[
                        { value: 'destinationTransportPort', label: 'destinationTransportPort' },
                        { value: 'sourceTransportPort', label: 'sourceTransportPort' },
                        { value: 'destinationCountryAlpha2', label: 'destinationCountryAlpha2' },
                        { value: 'sourceCountryAlpha2', label: 'sourceCountryAlpha2' },
                        { value: 'destinationServiceName', label: 'destinationServiceName' },
                        { value: 'sourceServiceName', label: 'sourceServiceName' },
                        { value: 'dnsType', label: 'dnsType' },
                        { value: 'ipProtocolName', label: 'ipProtocolName' },
                        { value: 'source', label: 'source' },
                        { value: 'tcpControlBits', label: 'tcpControlBits' },
                        { value: 'tcpControlFlags', label: 'tcpControlFlags' },
                      ]}
                      placeholder="Select source field"
                      className="min-w-[220px]"
                    />
                    <SelectField
                      label="Source Metric:"
                      value={deploymentForm.sourceMetric || ''}
                      onChange={(value) => handleDeploymentFormChange('sourceMetric', value)}
                      options={[
                        { value: 'flow', label: 'Flows' },
                        { value: 'packet', label: 'Packets' },
                        { value: 'octet', label: 'Bytes' },
                      ]}
                      placeholder="Select source metric"
                      className="min-w-[160px]"
                    />
                    <SelectField
                      label="Filter Type:"
                      value={deploymentForm.filterType || ''}
                      onChange={(value) => handleDeploymentFormChange('filterType', value)}
                      options={[
                        { value: 'range', label: 'range' },
                        { value: 'equal', label: 'equal' },
                      ]}
                      placeholder="Select filter type"
                      className="min-w-[160px]"
                    />
                    <TextField
                      label="Filter Value:"
                      value={deploymentForm.filterValue || ''}
                      onChange={(value) => handleDeploymentFormChange('filterValue', value)}
                      placeholder="Enter filter value"
                      className="w-48"
                    />
                    <Button
                      variant="outline"
                      type="button"
                      className="w-fit"
                    >
                      Advanced
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {deploymentForm.type === 'dga' && (
              <div className="rounded p-4 mt-2 border transition-colors duration-200 bg-surface-secondary border-border-primary text-sm text-text-secondary">
                No additional configuration options
              </div>
            )}
            {/* Status Messages */}
            {deploymentSubmitStatus === 'error' && (
              <Alert variant="danger">
                <AlertDescription>
                  Failed to create deployment. Please check all fields and try
                  again.
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-border-primary/20">
              <Button
                variant="secondary"
                onClick={handleCloseAddDeploymentModal}
                disabled={deploymentSubmitStatus === 'submitting'}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleCreateDeployment}
                loading={deploymentSubmitStatus === 'submitting'}
                disabled={
                  deploymentSubmitStatus === 'submitting' ||
                  !deploymentForm.name.trim() ||
                  !deploymentForm.type
                }
              >
                {deploymentSubmitStatus === 'submitting'
                  ? 'Creating...'
                  : 'Add'}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
